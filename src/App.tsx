import React, { useEffect } from 'react';
import TopBar from './components/TopBar/TopBar';
import ChatInterface from './components/ChatInterface/ChatInterface';
import InputArea from './components/InputArea/InputArea';
import { useSettingsStore } from './stores/settingsStore';
import { useModalStore } from './stores/modalStore';
import SettingsModal from './components/Modals/SettingsModal';
import ProviderModal from './components/Modals/ProviderModal';
import HistoryModal from './components/Modals/HistoryModal';
import { Toast } from './components/Common/Toast';

const App: React.FC = () => {
  const { theme, applyTheme } = useSettingsStore();
  const activeModal = useModalStore((s) => s.activeModal);

  useEffect(() => {
    applyTheme();
  }, [theme, applyTheme]);

  const renderModal = () => {
    switch (activeModal) {
      case 'settings':
        return <SettingsModal />;
      case 'provider':
        return <ProviderModal />;
      case 'history':
        return <HistoryModal />;
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col h-screen bg-adobe-bg text-adobe-text font-sans antialiased">
      <TopBar />
      <main className="flex-grow flex flex-col overflow-hidden">
        <ChatInterface />
        <InputArea />
      </main>
      {renderModal()}
      <Toast />
    </div>
  );
};

export default App;
