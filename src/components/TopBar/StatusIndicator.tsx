import React, { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';

const StatusIndicator: React.FC = () => {
    // Placeholder logic for status
    const [status, setStatus] = useState<'loading' | 'ok' | 'error'>('loading');
    const [latency, setLatency] = useState<number | null>(null);

    useEffect(() => {
        // Mock health check
        const interval = setInterval(() => {
            const start = Date.now();
            // Replace with actual health check from providerFactory
            Promise.resolve().then(() => {
                setStatus('ok');
                setLatency(Date.now() - start);
            }).catch(() => {
                setStatus('error');
                setLatency(null);
            });
        }, 30000);
        // Initial check
        setStatus('ok');
        setLatency(120);

        return () => clearInterval(interval);
    }, []);

    const colorMap = {
        loading: 'bg-yellow-500',
        ok: 'bg-green-500',
        error: 'bg-red-500',
    };
    
    return (
        <div className="flex items-center gap-2" title={`Status: ${status} | Latency: ${latency ?? 'N/A'}ms`}>
            {status === 'loading' ? <Loader2 size={14} className="animate-spin" /> : <div className={`w-3 h-3 rounded-full ${colorMap[status]}`} />}
        </div>
    );
};

export default StatusIndicator;
