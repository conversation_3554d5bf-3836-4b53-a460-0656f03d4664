import React, { useState, useEffect, useCallback } from 'react';
import { Loader2 } from 'lucide-react';
import { useSettingsStore } from '../../stores/settingsStore';
import { getProviderApi } from '../../services/providerFactory';
import { ProviderID } from '../../types';

type Status = 'loading' | 'ok' | 'error' | 'idle';

const StatusIndicator: React.FC = () => {
    const selectedProvider = useSettingsStore(s => s.selectedProvider);
    const [status, setStatus] = useState<Status>('idle');
    const [latency, setLatency] = useState<number | null>(null);

    const checkConnection = useCallback(async () => {
      if (!selectedProvider) {
          setStatus('idle');
          return;
      }
      setStatus('loading');
      const start = Date.now();
      try {
        const api = getProviderApi(selectedProvider as ProviderID);
        // Using getModels as a health check is a common pattern
        // when a dedicated /health endpoint is not available.
        await api.getModels();
        setLatency(Date.now() - start);
        setStatus('ok');
      } catch (e) {
        console.warn(`Health check failed for ${selectedProvider}:`, e);
        setStatus('error');
        setLatency(null);
      }
    }, [selectedProvider]);

    useEffect(() => {
        checkConnection(); // Initial check
        const interval = setInterval(checkConnection, 30000); // Check every 30 seconds
        return () => clearInterval(interval);
    }, [checkConnection]);

    const colorMap: Record<Status, string> = {
        idle: 'bg-gray-400',
        loading: 'bg-yellow-500 animate-pulse',
        ok: 'bg-green-500',
        error: 'bg-red-500',
    };

    const titleMap: Record<Status, string> = {
        idle: 'Status: Idle',
        loading: 'Status: Checking connection...',
        ok: `Status: Connected | Latency: ${latency ?? 'N/A'}ms`,
        error: 'Status: Connection failed',
    };

    return (
        <div className="flex items-center gap-2" title={titleMap[status]}>
            {status === 'loading' ?
              <Loader2 size={14} className="animate-spin text-yellow-500" /> :
              <div className={`w-3 h-3 rounded-full ${colorMap[status]}`} />
            }
        </div>
    );
};

export default StatusIndicator;
