import React from 'react';
import { Plus, History, Settings, Loader2 } from 'lucide-react';
import { useModalStore } from '../../stores/modalStore';
import { useChatStore } from '../../stores/chatStore';
import StatusIndicator from './StatusIndicator';
import { useSettingsStore } from '../../stores/settingsStore';

const TopBar: React.FC = () => {
  const openModal = useModalStore((s) => s.openModal);
  const startNewConversation = useChatStore(s => s.startNewConversation);
  const { selectedProvider, selectedModel } = useSettingsStore();

  return (
    <header className="flex items-center justify-between p-2 border-b border-adobe bg-adobe-secondary flex-shrink-0">
      <div className="flex items-center gap-2">
        <StatusIndicator />
        <div>
            <p className="text-xs font-bold truncate">{selectedProvider}</p>
            <p className="text-xs text-gray-400 truncate">{selectedModel || 'No model selected'}</p>
        </div>
      </div>
      <div className="flex items-center gap-1">
        <button onClick={startNewConversation} className="p-1.5 rounded hover:bg-adobe-bg" aria-label="New Chat"><Plus size={16} /></button>
        <button onClick={() => openModal('history')} className="p-1.5 rounded hover:bg-adobe-bg" aria-label="Chat History"><History size={16} /></button>
        <button onClick={() => openModal('settings')} className="p-1.5 rounded hover:bg-adobe-bg" aria-label="Settings"><Settings size={16} /></button>
      </div>
    </header>
  );
};

export default TopBar;
