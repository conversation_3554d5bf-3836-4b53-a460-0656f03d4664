import React from 'react';
import { Message } from '../../types';
import CodeBlock from './CodeBlock';
import { User, <PERSON><PERSON> } from 'lucide-react';

const MessageBubble: React.FC<{ message: Message }> = ({ message }) => {
  const isUser = message.role === 'user';
  
  // Basic markdown detection for code blocks
  const parts = message.content.split(/(```[\w\s]*\n[\s\S]*?\n```)/g);

  return (
    <div className={`flex items-start gap-3 ${isUser ? 'justify-end' : ''}`}>
      {!isUser && <div className="flex-shrink-0 w-8 h-8 rounded-full bg-adobe-secondary flex items-center justify-center"><Bot size={18} /></div>}
      <div className={`max-w-[85%] rounded-lg p-3 ${isUser ? 'bg-blue-600 text-white' : 'bg-adobe-secondary'}`}>
        {parts.map((part, index) => {
          if (part.startsWith('```')) {
            const match = part.match(/```(\w*)\n([\s\S]*?)\n```/);
            if (match) {
              const lang = match[1] || 'plaintext';
              const code = match[2];
              return <CodeBlock key={index} code={code} language={lang} />;
            }
          }
          return <p key={index} className="whitespace-pre-wrap">{part}</p>;
        })}
        <div className="text-xs opacity-60 mt-2 text-right">
          {new Date(message.timestamp).toLocaleTimeString()}
        </div>
      </div>
      {isUser && <div className="flex-shrink-0 w-8 h-8 rounded-full bg-adobe-secondary flex items-center justify-center"><User size={18} /></div>}
    </div>
  );
};

export default MessageBubble;
