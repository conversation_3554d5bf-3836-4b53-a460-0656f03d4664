import React from 'react';
import Modal from '../Common/Modal';
import { useSettingsStore } from '../../stores/settingsStore';

const SettingsModal: React.FC = () => {
  const { theme, setTheme } = useSettingsStore();

  return (
    <Modal title="Settings">
      <div className="flex flex-col gap-4">
        <div>
          <label htmlFor="theme-select" className="block text-sm font-medium mb-1">Theme</label>
          <select 
            id="theme-select"
            value={theme}
            onChange={(e) => setTheme(e.target.value as any)}
            className="w-full p-2 rounded bg-adobe-secondary border border-adobe focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="auto">Auto (Sync with Adobe)</option>
            <option value="light">Light</option>
            <option value="dark">Dark</option>
          </select>
        </div>
        {/* Add more settings here */}
      </div>
    </Modal>
  );
};

export default SettingsModal;
