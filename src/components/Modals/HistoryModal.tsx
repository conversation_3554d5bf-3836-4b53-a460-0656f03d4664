import React from 'react';
import Modal from '../Common/Modal';
import { useChatStore } from '../../stores/chatStore';

const HistoryModal: React.FC = () => {
  const conversations = useChatStore(s => s.conversations);

  return (
    <Modal title="Chat History">
      {conversations.length === 0 ? (
        <p>No chat history yet.</p>
      ) : (
        <ul className="flex flex-col gap-2">
          {conversations.map(convo => (
            <li key={convo.id} className="p-2 rounded hover:bg-adobe-secondary cursor-pointer">
              <p className="font-semibold">{convo.title}</p>
              <p className="text-xs opacity-70">{new Date(convo.createdAt).toLocaleString()}</p>
            </li>
          ))}
        </ul>
      )}
    </Modal>
  );
};

export default HistoryModal;
