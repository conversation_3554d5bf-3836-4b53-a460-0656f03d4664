import React, { useState, useRef, KeyboardEvent, useEffect } from 'react';
import { Send, Paperclip, Mic, Loader2 } from 'lucide-react';
import { useChatStore } from '../../stores/chatStore';

const InputArea: React.FC = () => {
  const [text, setText] = useState('');
  const { sendChatMessage, isLoading } = useChatStore();
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSend = () => {
    if (text.trim() && !isLoading) {
      sendChatMessage(text.trim());
      setText('');
      // Delay focus to allow UI to update
      setTimeout(() => textareaRef.current?.focus(), 0);
    }
  };
  
  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        handleSend();
    }
  };

  // Auto-resize textarea
  useEffect(() => {
    if(textareaRef.current) {
        textareaRef.current.style.height = 'auto';
        const scrollHeight = textareaRef.current.scrollHeight;
        textareaRef.current.style.height = `${scrollHeight}px`;
    }
  }, [text]);

  return (
    <div className="p-2 border-t border-adobe bg-adobe-bg flex-shrink-0">
      <div className="flex items-start gap-2 p-2 rounded-md bg-adobe-secondary">
        <button className="p-1.5 rounded" aria-label="Attach File"><Paperclip size={18} /></button>
        <textarea
          ref={textareaRef}
          value={text}
          onChange={(e) => setText(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Type a message or drop a file..."
          className="flex-grow bg-transparent focus:outline-none resize-none max-h-48 overflow-y-auto"
          rows={1}
          maxLength={4000}
          disabled={isLoading}
        />
        <button onClick={handleSend} disabled={isLoading || !text.trim()} className="p-1.5 rounded bg-blue-600 text-white disabled:bg-gray-500 self-end">
          {isLoading ? <Loader2 size={18} className="animate-spin" /> : <Send size={18} />}
        </button>
      </div>
      <div className="text-xs text-right mt-1 opacity-70 pr-2">
        {text.length} / 4000
      </div>
    </div>
  );
};

export default InputArea;
