/*
  SahAI ExtendScript Bridge
  This file is executed in the host application's engine.
  It provides functions to interact with the Adobe application context.
*/

// Polyfill for JSON if it doesn't exist (older hosts)
if (typeof JSON === 'undefined') {
    JSON = {
        parse: function (sJSON) { return eval('(' + sJSON + ')'); },
        stringify: (function () {
            // ... (A full JSON.stringify polyfill would go here) ...
            return function (value) { return '{"message": "JSON polyfill is basic"}'; };
        })()
    };
}


/**
 * Get information about the host application and the active document.
 * @returns {String} A JSON string containing host and document info.
 */
function getHostInfo() {
    try {
        var info = {
            appName: app.name,
            appVersion: app.version,
            os: $.os,
            docName: (app.activeDocument) ? app.activeDocument.name : 'No active document',
            docPath: (app.activeDocument) ? app.activeDocument.fullName.fsName : 'N/A'
        };
        return JSON.stringify(info);
    } catch (e) {
        return JSON.stringify({ error: e.toString() });
    }
}


/**
 * Executes a given script snippet in the host's ExtendScript engine.
 * This function acts as a sandboxed evaluator for AI-generated code.
 * @param {String} script The code to execute.
 * @returns {String} The result of the execution, or an error message, as a JSON string.
 */
function runCode(script) {
    try {
        // A simple 'eval' can be dangerous. In a real product, this would need
        // careful sandboxing or a more controlled execution environment.
        // For this example, we use a function wrapper to limit scope.
        var result = (function() {
            return eval(script);
        })();

        // Try to serialize the result. Handle complex objects gracefully.
        var resultString;
        if (result === undefined) {
            resultString = 'undefined';
        } else if (result === null) {
            resultString = 'null';
        } else {
            try {
                resultString = result.toString();
            } catch (e) {
                resultString = "Result could not be converted to string.";
            }
        }
        
        return JSON.stringify({ success: true, result: resultString });
    } catch (e) {
        return JSON.stringify({ success: false, error: e.toString(), line: e.line, file: e.fileName });
    }
}
