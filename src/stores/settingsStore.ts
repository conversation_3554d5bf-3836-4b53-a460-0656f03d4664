import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ProviderID, ProviderConfig, AdobeTheme } from '../types';
import { setSecureCredential, getSecureCredential } from '../utils/cep';

interface SettingsState {
  providers: Record<ProviderID, ProviderConfig>;
  selectedProvider: ProviderID;
  selectedModel: string;
  theme: 'light' | 'dark' | 'auto';
  adobeTheme: AdobeTheme | null;
  setProviderApiKey: (providerId: ProviderID, apiKey: string) => void;
  setSelectedProvider: (providerId: ProviderID) => void;
  setSelectedModel: (modelId: string) => void;
  setTheme: (theme: 'light' | 'dark' | 'auto', adobeTheme?: AdobeTheme) => void;
  applyTheme: () => void;
}

const initialProviders: Record<ProviderID, ProviderConfig> = {
  openai: { models: [] },
  anthropic: { models: [] },
  google: { models: [] },
  groq: { models: [] },
  deepseek: { models: [] },
  openrouter: { models: [] },
  ollama: { baseURL: 'http://localhost:11434', models: [] },
};

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      providers: initialProviders,
      selectedProvider: 'openai',
      selectedModel: '',
      theme: 'auto',
      adobeTheme: null,

      setProviderApiKey: (providerId, apiKey) => {
        setSecureCredential(providerId, apiKey);
        // We don't store the key in the state itself for security
        // Re-fetch or update models after setting the key if necessary
      },
      
      setSelectedProvider: (providerId) => set({ selectedProvider: providerId, selectedModel: '' }),
      
      setSelectedModel: (modelId) => set({ selectedModel: modelId }),
      
      setTheme: (theme, adobeTheme) => set({ theme, ...(adobeTheme && { adobeTheme }) }),
      
      applyTheme: () => {
        const { theme, adobeTheme } = get();
        const root = document.documentElement;
        if ((theme === 'auto' && adobeTheme) || theme !== 'auto') {
            const isDark = theme === 'dark' || (theme === 'auto' && (adobeTheme?.backgroundColor ?? '#FFFFFF') < '#808080');
            if (isDark) {
                root.style.setProperty('--adobe-bg-color', '#323232');
                root.style.setProperty('--adobe-text-color', '#F0F0F0');
                root.style.setProperty('--adobe-secondary-bg-color', '#3C3C3C');
                root.style.setProperty('--adobe-border-color', '#4A4A4A');
                root.style.setProperty('--adobe-scrollbar-thumb-color', '#555555');
                root.style.setProperty('--adobe-scrollbar-track-color', '#323232');
            } else {
                root.style.setProperty('--adobe-bg-color', '#F5F5F5');
                root.style.setProperty('--adobe-text-color', '#1a1a1a');
                root.style.setProperty('--adobe-secondary-bg-color', '#EAEAEA');
                root.style.setProperty('--adobe-border-color', '#D3D3D3');
                root.style.setProperty('--adobe-scrollbar-thumb-color', '#C1C1C1');
                root.style.setProperty('--adobe-scrollbar-track-color', '#F5F5F5');
            }
        }
      },
    }),
    {
      name: 'sahai-settings-storage',
      // Only persist non-sensitive settings
      partialize: (state) => ({
        selectedProvider: state.selectedProvider,
        selectedModel: state.selectedModel,
        theme: state.theme,
      }),
    }
  )
);

// Load API keys from secure storage on startup
Object.keys(initialProviders).forEach(id => {
    const key = getSecureCredential(id as ProviderID);
    if (key) {
        // You might trigger a state update or just let services use getSecureCredential directly
        console.log(`API key loaded for ${id}`);
    }
});
