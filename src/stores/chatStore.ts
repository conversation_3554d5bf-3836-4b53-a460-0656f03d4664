import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { v4 as uuidv4 } from 'uuid';
import { Conversation, Message, ProviderID } from '../types';
import { getProviderApi } from '../services/providerFactory';
import { useToastStore } from './toastStore';

interface ChatState {
  conversations: Record<string, Conversation>;
  currentConversationId: string | null;
  isLoading: boolean;
  addMessage: (message: Omit<Message, 'id' | 'timestamp'>, conversationId: string) => void;
  appendTokenToLastMessage: (token: string) => void;
  sendChatMessage: (content: string) => Promise<void>;
  startNewConversation: () => void;
  setCurrentConversationId: (id: string) => void;
  clearAllConversations: () => void;
}

const createNewConversation = (): Conversation => ({
  id: uuidv4(),
  title: 'New Chat',
  messages: [],
  createdAt: new Date().toISOString(),
});

export const useChatStore = create<ChatState>()(
  persist(
    (set, get) => ({
      conversations: {},
      currentConversationId: null,
      isLoading: false,

      addMessage: (messageContent, conversationId) => {
        set((state) => {
          const conversation = state.conversations[conversationId];
          if (!conversation) return {};

          const newMessage: Message = {
            ...messageContent,
            id: uuidv4(),
            timestamp: new Date().toISOString(),
          };

          return {
            conversations: {
              ...state.conversations,
              [conversationId]: {
                ...conversation,
                messages: [...conversation.messages, newMessage],
              },
            },
          };
        });
      },

      appendTokenToLastMessage: (token) => {
          set(state => {
              const { currentConversationId, conversations } = state;
              if (!currentConversationId || !conversations[currentConversationId]) return {};

              const currentMessages = conversations[currentConversationId].messages;
              if (currentMessages.length === 0) return {};

              const lastMessage = currentMessages[currentMessages.length - 1];
              if (lastMessage.role !== 'assistant') return {};

              const updatedLastMessage = { ...lastMessage, content: lastMessage.content + token };

              return {
                  conversations: {
                      ...state.conversations,
                      [currentConversationId]: {
                          ...conversations[currentConversationId],
                          messages: [...currentMessages.slice(0, -1), updatedLastMessage]
                      }
                  }
              }
          })
      },

      sendChatMessage: async (content: string) => {
        let { currentConversationId } = get();

        // If there's no active conversation, create one
        if (!currentConversationId) {
            const newConvo = createNewConversation();
            set(state => ({
                conversations: { ...state.conversations, [newConvo.id]: newConvo },
                currentConversationId: newConvo.id
            }));
            currentConversationId = newConvo.id;
        }

        // Add user message
        get().addMessage({ role: 'user', content }, currentConversationId!);

        // Add empty assistant message placeholder
        get().addMessage({ role: 'assistant', content: '' }, currentConversationId!);

        set({ isLoading: true });

        try {
          const { useSettingsStore } = await import('./settingsStore');
          const { selectedProvider, selectedModel } = useSettingsStore.getState();
          const api = getProviderApi(selectedProvider as ProviderID);
          const messages = get().conversations[currentConversationId!].messages.slice(0, -1); // Exclude the placeholder

          const stream = api.chat(messages, selectedModel);
          for await (const chunk of stream) {
            get().appendTokenToLastMessage(chunk);
          }
        } catch (error: any) {
            get().appendTokenToLastMessage(`\n\n**Error:** ${error.message}`);
            useToastStore.getState().addToast({ message: error.message, type: 'error' });
        } finally {
            set({ isLoading: false });
        }
      },

      startNewConversation: () => {
        const newConvo = createNewConversation();
        set(state => ({
            conversations: { ...state.conversations, [newConvo.id]: newConvo },
            currentConversationId: newConvo.id
        }));
      },

      setCurrentConversationId: (id) => set({ currentConversationId: id }),

      clearAllConversations: () => set({ conversations: {}, currentConversationId: null }),

    }),
    {
      name: 'sahai-chat-storage',
    }
  )
);
