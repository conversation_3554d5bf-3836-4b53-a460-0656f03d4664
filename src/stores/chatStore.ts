import { create } from 'zustand';
import { Conversation, Message } from '../types';
import { v4 as uuidv4 } from 'uuid'; // requires `npm install uuid @types/uuid`

interface ChatState {
  conversations: Conversation[];
  currentConversationId: string | null;
  messages: Message[];
  isLoading: boolean;
  addMessage: (message: Omit<Message, 'id' | 'timestamp'>) => void;
  startLoading: () => void;
  stopLoading: () => void;
  createNewConversation: () => void;
}

export const useChatStore = create<ChatState>((set, get) => ({
  conversations: [],
  currentConversationId: null,
  messages: [],
  isLoading: false,
  
  addMessage: (messageContent) => {
    const newMessage: Message = {
      ...messageContent,
      id: uuidv4(),
      timestamp: new Date().toISOString(),
    };
    set((state) => ({ messages: [...state.messages, newMessage] }));
  },

  startLoading: () => set({ isLoading: true }),
  stopLoading: () => set({ isLoading: false }),
  
  createNewConversation: () => {
    const newConversation: Conversation = {
      id: uuidv4(),
      title: 'New Chat',
      messages: [],
      createdAt: new Date().toISOString(),
    };
    set(state => ({
      conversations: [...state.conversations, newConversation],
      currentConversationId: newConversation.id,
      messages: [], // Clear messages for new chat
    }));
  }
}));
